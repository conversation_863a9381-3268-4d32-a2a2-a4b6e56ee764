{"name": "vencord", "className": "DataModel", "filePaths": ["default.project.json"], "children": [{"name": "ReplicatedStorage", "className": "ReplicatedStorage", "children": [{"name": "Packages", "className": "Folder", "children": [{"name": "<PERSON><PERSON>", "className": "ModuleScript", "filePaths": ["Packages\\Janitor.lua"]}, {"name": "_Index", "className": "Folder", "children": [{"name": "evaera_promise@4.0.0", "className": "Folder", "children": [{"name": "promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\evaera_promise@4.0.0\\promise\\lib\\init.lua", "Packages\\_Index\\evaera_promise@4.0.0\\promise\\default.project.json"], "children": [{"name": "init.spec", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\evaera_promise@4.0.0\\promise\\lib\\init.spec.lua"]}]}]}, {"name": "howmanys<PERSON><PERSON>_janitor@1.18.3", "className": "Folder", "children": [{"name": "Promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\Promise.lua"]}, {"name": "janitor", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\init.luau", "Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\default.project.json"], "children": [{"name": "FastDefer", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\FastDefer.luau"]}, {"name": "Promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\Promise.luau"]}, {"name": "__tests__", "className": "Folder", "children": [{"name": "Janitor.test", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\__tests__\\Janitor.test.luau"]}]}, {"name": "jest.config", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\jest.config.luau"]}]}]}, {"name": "howmanysmall_typed-promise@4.0.6", "className": "Folder", "children": [{"name": "Promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_typed-promise@4.0.6\\Promise.lua"]}, {"name": "typed-promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_typed-promise@4.0.6\\typed-promise\\src\\init.luau", "Packages\\_Index\\howmanysmall_typed-promise@4.0.6\\typed-promise\\default.project.json"]}]}]}]}, {"name": "Shared", "className": "Folder", "children": [{"name": "Types", "className": "Folder", "children": [{"name": "ProfileTypes", "className": "ModuleScript", "filePaths": ["src/shared\\Types\\ProfileTypes.luau"]}]}, {"name": "Utilities", "className": "ModuleScript", "filePaths": ["src/shared\\Utilities.luau"]}]}]}, {"name": "ServerScriptService", "className": "ServerScriptService", "children": [{"name": "Server", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\init.server.luau"], "children": [{"name": "DataStore", "className": "ModuleScript", "filePaths": ["src/server\\DataStore\\init.luau"], "children": [{"name": "ProfileManager", "className": "ModuleScript", "filePaths": ["src/server\\DataStore\\ProfileManager.luau"]}, {"name": "ProfileTemplate", "className": "ModuleScript", "filePaths": ["src/server\\DataStore\\ProfileTemplate.luau"]}]}]}, {"name": "ServerPackages", "className": "Folder", "children": [{"name": "ProfileService", "className": "ModuleScript", "filePaths": ["ServerPackages\\ProfileService.lua"]}, {"name": "_Index", "className": "Folder", "children": [{"name": "firebird702_profileservice@1.1.0", "className": "Folder", "children": [{"name": "profileservice", "className": "ModuleScript", "filePaths": ["ServerPackages\\_Index\\firebird702_profileservice@1.1.0\\profileservice\\src\\init.lua", "ServerPackages\\_Index\\firebird702_profileservice@1.1.0\\profileservice\\default.project.json"]}]}]}]}]}, {"name": "StarterPlayer", "className": "StarterPlayer", "children": [{"name": "StarterPlayerScripts", "className": "StarterPlayerScripts", "children": [{"name": "Client", "className": "LocalScript", "filePaths": ["src/client\\init.client.luau"]}]}]}]}