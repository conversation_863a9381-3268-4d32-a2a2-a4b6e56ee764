{"name": "vencord", "className": "DataModel", "filePaths": ["default.project.json"], "children": [{"name": "ReplicatedStorage", "className": "ReplicatedStorage", "children": [{"name": "Packages", "className": "Folder", "children": [{"name": "<PERSON><PERSON>", "className": "ModuleScript", "filePaths": ["Packages\\Janitor.lua"]}, {"name": "ObjectPool", "className": "ModuleScript", "filePaths": ["Packages\\ObjectPool.lua"]}, {"name": "ShapeCastHitbox", "className": "ModuleScript", "filePaths": ["Packages\\ShapeCastHitbox.lua"]}, {"name": "_Index", "className": "Folder", "children": [{"name": "evaera_promise@4.0.0", "className": "Folder", "children": [{"name": "promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\evaera_promise@4.0.0\\promise\\lib\\init.lua", "Packages\\_Index\\evaera_promise@4.0.0\\promise\\default.project.json"], "children": [{"name": "init.spec", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\evaera_promise@4.0.0\\promise\\lib\\init.spec.lua"]}]}]}, {"name": "frqstbite_object-pool@1.0.2", "className": "Folder", "children": [{"name": "object-pool", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\frqstbite_object-pool@1.0.2\\object-pool\\lib\\init.luau", "Packages\\_Index\\frqstbite_object-pool@1.0.2\\object-pool\\default.project.json"]}]}, {"name": "howmanys<PERSON><PERSON>_janitor@1.18.3", "className": "Folder", "children": [{"name": "Promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\Promise.lua"]}, {"name": "janitor", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\init.luau", "Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\default.project.json"], "children": [{"name": "FastDefer", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\FastDefer.luau"]}, {"name": "Promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\Promise.luau"]}, {"name": "__tests__", "className": "Folder", "children": [{"name": "Janitor.test", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\__tests__\\Janitor.test.luau"]}]}, {"name": "jest.config", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_janitor@1.18.3\\janitor\\src\\jest.config.luau"]}]}]}, {"name": "howmanysmall_typed-promise@4.0.6", "className": "Folder", "children": [{"name": "Promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_typed-promise@4.0.6\\Promise.lua"]}, {"name": "typed-promise", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\howmanysmall_typed-promise@4.0.6\\typed-promise\\src\\init.luau", "Packages\\_Index\\howmanysmall_typed-promise@4.0.6\\typed-promise\\default.project.json"]}]}, {"name": "teamswordphin_shapecasthitbox@0.2.4", "className": "Folder", "children": [{"name": "shapecasthitbox", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\init.luau", "Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\default.project.json"], "children": [{"name": "Hitbox", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Hitbox.luau"]}, {"name": "Settings", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Settings.luau"]}, {"name": "<PERSON><PERSON>", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Solvers\\init.luau"], "children": [{"name": "Attachment", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Solvers\\Attachment.luau"]}, {"name": "Blockcast", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Solvers\\Blockcast.luau"]}, {"name": "Raycast", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Solvers\\Raycast.luau"]}, {"name": "Spherecast", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Solvers\\Spherecast.luau"]}]}, {"name": "Types", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Types.luau"]}, {"name": "Visualizers", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Visualizers\\init.luau"], "children": [{"name": "Blockcast", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Visualizers\\Blockcast.luau"]}, {"name": "Raycast", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Visualizers\\Raycast.luau"]}, {"name": "Spherecast", "className": "ModuleScript", "filePaths": ["Packages\\_Index\\teamswordphin_shapecasthitbox@0.2.4\\shapecasthitbox\\src\\Visualizers\\Spherecast.luau"]}]}]}]}]}]}, {"name": "Shared", "className": "Folder", "children": [{"name": "Types", "className": "Folder", "children": [{"name": "ProfileTypes", "className": "ModuleScript", "filePaths": ["src/shared\\Types\\ProfileTypes.luau"]}, {"name": "SwordTypes", "className": "ModuleScript", "filePaths": ["src/shared\\Types\\SwordTypes.luau"]}]}, {"name": "Utilities", "className": "ModuleScript", "filePaths": ["src/shared\\Utilities.luau"]}]}]}, {"name": "ServerScriptService", "className": "ServerScriptService", "children": [{"name": "Server", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server\\init.server.luau"], "children": [{"name": "DataStore", "className": "ModuleScript", "filePaths": ["src/server\\DataStore\\init.luau"], "children": [{"name": "LeaderstatsManager", "className": "ModuleScript", "filePaths": ["src/server\\DataStore\\LeaderstatsManager.luau"]}, {"name": "ProfileManager", "className": "ModuleScript", "filePaths": ["src/server\\DataStore\\ProfileManager.luau"]}, {"name": "ProfileTemplate", "className": "ModuleScript", "filePaths": ["src/server\\DataStore\\ProfileTemplate.luau"]}]}, {"name": "SwordSystem", "className": "ModuleScript", "filePaths": ["src/server\\SwordSystem\\init.luau"], "children": [{"name": "CombatHandler", "className": "ModuleScript", "filePaths": ["src/server\\SwordSystem\\CombatHandler.luau"]}, {"name": "RemoteEvents", "className": "ModuleScript", "filePaths": ["src/server\\SwordSystem\\RemoteEvents.luau"]}, {"name": "SwordManager", "className": "ModuleScript", "filePaths": ["src/server\\SwordSystem\\SwordManager.luau"]}, {"name": "SwordScript", "className": "ModuleScript", "filePaths": ["src/server\\SwordSystem\\SwordScript.luau"]}]}]}, {"name": "ServerPackages", "className": "Folder", "children": [{"name": "ProfileService", "className": "ModuleScript", "filePaths": ["ServerPackages\\ProfileService.lua"]}, {"name": "_Index", "className": "Folder", "children": [{"name": "firebird702_profileservice@1.1.0", "className": "Folder", "children": [{"name": "profileservice", "className": "ModuleScript", "filePaths": ["ServerPackages\\_Index\\firebird702_profileservice@1.1.0\\profileservice\\src\\init.lua", "ServerPackages\\_Index\\firebird702_profileservice@1.1.0\\profileservice\\default.project.json"]}]}]}]}]}, {"name": "StarterPlayer", "className": "StarterPlayer", "children": [{"name": "StarterPlayerScripts", "className": "StarterPlayerScripts", "children": [{"name": "Client", "className": "LocalScript", "filePaths": ["src/client\\init.client.luau"], "children": [{"name": "SwordAnimations", "className": "LocalScript", "filePaths": ["src/client\\SwordAnimations.client.luau"]}]}]}]}]}