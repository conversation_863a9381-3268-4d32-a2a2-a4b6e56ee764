local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local character = player.CharacterAdded:Wait()
local humanoid = character:WaitForChild("Humanoid")

local currentTool = nil
local idleAnimation = nil
local swingAnimation = nil

local IDLE_ANIMATION_ID = "rbxassetid://507766388"
local SWING_ANIMATION_ID = "rbxassetid://507766666"

local function createAnimations()
	if not humanoid then return end
	
	if idleAnimation then
		idleAnimation:Stop()
		idleAnimation:Destroy()
	end
	
	if swingAnimation then
		swingAnimation:Stop()
		swingAnimation:Destroy()
	end
	
	local idleAnimationObject = Instance.new("Animation")
	idleAnimationObject.AnimationId = IDLE_ANIMATION_ID
	idleAnimation = humanoid:LoadAnimation(idleAnimationObject)
	idleAnimation.Looped = true
	idleAnimation.Priority = Enum.AnimationPriority.Action
	
	local swingAnimationObject = Instance.new("Animation")
	swingAnimationObject.AnimationId = SWING_ANIMATION_ID
	swingAnimation = humanoid:LoadAnimation(swingAnimationObject)
	swingAnimation.Looped = false
	swingAnimation.Priority = Enum.AnimationPriority.Action2
end

local function onToolEquipped(tool)
	currentTool = tool
	createAnimations()
	
	if idleAnimation then
		idleAnimation:Play()
	end
	
	local handle = tool:FindFirstChild("Handle")
	if handle then
		local equipSound = Instance.new("Sound")
		equipSound.SoundId = "rbxasset://sounds/unsheath.mp3"
		equipSound.Volume = 0.5
		equipSound.Parent = handle
		equipSound:Play()
		
		equipSound.Ended:Connect(function()
			equipSound:Destroy()
		end)
	end
end

local function onToolUnequipped()
	currentTool = nil
	
	if idleAnimation then
		idleAnimation:Stop()
	end
	
	if swingAnimation then
		swingAnimation:Stop()
	end
end

local function onToolActivated()
	if swingAnimation and not swingAnimation.IsPlaying then
		swingAnimation:Play()
		
		if currentTool then
			local handle = currentTool:FindFirstChild("Handle")
			if handle then
				local swingSound = Instance.new("Sound")
				swingSound.SoundId = "rbxasset://sounds/swordslash.mp3"
				swingSound.Volume = 0.3
				swingSound.Parent = handle
				swingSound:Play()
				
				swingSound.Ended:Connect(function()
					swingSound:Destroy()
				end)
			end
		end
	end
end

local function onCharacterAdded(newCharacter)
	character = newCharacter
	humanoid = character:WaitForChild("Humanoid")
	
	currentTool = nil
	idleAnimation = nil
	swingAnimation = nil
end

local function onChildAdded(child)
	if child:IsA("Tool") and child.Name:match("^Sword_") then
		child.Equipped:Connect(function()
			onToolEquipped(child)
		end)
		
		child.Unequipped:Connect(function()
			onToolUnequipped()
		end)
		
		child.Activated:Connect(function()
			onToolActivated()
		end)
	end
end

local function onChildRemoved(child)
	if child == currentTool then
		onToolUnequipped()
	end
end

player.CharacterAdded:Connect(onCharacterAdded)

if character then
	local backpack = player:WaitForChild("Backpack")
	backpack.ChildAdded:Connect(onChildAdded)
	backpack.ChildRemoved:Connect(onChildRemoved)
	
	for _, tool in backpack:GetChildren() do
		onChildAdded(tool)
	end
	
	character.ChildAdded:Connect(onChildAdded)
	character.ChildRemoved:Connect(onChildRemoved)
	
	for _, tool in character:GetChildren() do
		onChildAdded(tool)
	end
end
