local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local ProfileService = require(game:GetService("ServerScriptService").ServerPackages.ProfileService)
local ProfileTemplate = require(script.Parent.ProfileTemplate)
local ProfileTypes = require(game:GetService("ReplicatedStorage").Shared.Types.ProfileTypes)

type Profile = ProfileTypes.Profile

local ProfileManager = {}

local STORE_NAME = "PlayerData"
local STORE_SCOPE = "Production"

local ProfileStore = ProfileService.GetProfileStore({
	Name = STORE_NAME,
	Scope = STORE_SCOPE,
}, ProfileTemplate)

if RunService:IsStudio() then
	ProfileStore = ProfileStore.Mock
end

local LoadedProfiles: { [Player]: Profile } = {}

local function onProfileRelease(player: Player)
	return function(placeId: number?, gameJobId: string?)
		LoadedProfiles[player] = nil
		if player.Parent == Players then
			player:Kick("Profile released - rejoin to continue playing")
		end
	end
end

local function processGlobalUpdates(profile: Profile)
	for _, update in profile.GlobalUpdates:GetActiveUpdates() do
		profile.GlobalUpdates:LockActiveUpdate(update[1])
	end
	
	for _, update in profile.GlobalUpdates:GetLockedUpdates() do
		local updateId = update[1]
		local updateData = update[2]
		
		if updateData.Type == "Cash" then
			profile.Data.Cash += updateData.Amount or 0
		elseif updateData.Type == "Experience" then
			profile.Data.Experience += updateData.Amount or 0
		elseif updateData.Type == "Item" then
			local itemName = updateData.ItemName
			local amount = updateData.Amount or 1
			if itemName then
				profile.Data.Inventory.Items[itemName] = (profile.Data.Inventory.Items[itemName] or 0) + amount
			end
		end
		
		profile.GlobalUpdates:ClearLockedUpdate(updateId)
	end
end

local function setupGlobalUpdateListeners(profile: Profile)
	profile.GlobalUpdates:ListenToNewActiveUpdate(function(updateId: number, updateData: any)
		profile.GlobalUpdates:LockActiveUpdate(updateId)
	end)
	
	profile.GlobalUpdates:ListenToNewLockedUpdate(function(updateId: number, updateData: any)
		if updateData.Type == "Cash" then
			profile.Data.Cash += updateData.Amount or 0
		elseif updateData.Type == "Experience" then
			profile.Data.Experience += updateData.Amount or 0
		elseif updateData.Type == "Item" then
			local itemName = updateData.ItemName
			local amount = updateData.Amount or 1
			if itemName then
				profile.Data.Inventory.Items[itemName] = (profile.Data.Inventory.Items[itemName] or 0) + amount
			end
		end
		
		profile.GlobalUpdates:ClearLockedUpdate(updateId)
	end)
end

function ProfileManager.loadProfileAsync(player: Player): Profile?
	local profileKey = `Player_{player.UserId}`
	local profile = ProfileStore:LoadProfileAsync(profileKey)
	
	if profile == nil then
		return nil
	end
	
	profile:AddUserId(player.UserId)
	profile:Reconcile()
	profile:ListenToRelease(onProfileRelease(player))
	
	if player.Parent ~= Players then
		profile:Release()
		return nil
	end
	
	LoadedProfiles[player] = profile
	
	profile.Data.Stats.LoginCount += 1
	profile.Data.Stats.LastLogin = os.time()
	
	processGlobalUpdates(profile)
	setupGlobalUpdateListeners(profile)
	
	return profile
end

function ProfileManager.getProfile(player: Player): Profile?
	return LoadedProfiles[player]
end

function ProfileManager.releaseProfile(player: Player)
	local profile = LoadedProfiles[player]
	if profile then
		profile:Release()
		LoadedProfiles[player] = nil
	end
end

function ProfileManager.getAllProfiles(): { [Player]: Profile }
	return LoadedProfiles
end

function ProfileManager.sendGlobalUpdate(targetUserId: number, updateData: any)
	local targetProfileKey = `Player_{targetUserId}`
	ProfileStore:GlobalUpdateProfileAsync(targetProfileKey, function(globalUpdates)
		globalUpdates:AddActiveUpdate(updateData)
	end)
end

function ProfileManager.shutdown()
	for player, profile in LoadedProfiles do
		profile:Release()
		LoadedProfiles[player] = nil
	end
end

return ProfileManager
