local ProfileTypes = require(game:GetService("ReplicatedStorage").Shared.Types.ProfileTypes)

local ProfileTemplate: ProfileTypes.PlayerData = {
	TotalPoints = 0,
	Gold = 100,
	Gems = 0,
	Level = 1,
	Experience = 0,
	Inventory = {
		Items = {},
		Equipment = {
			Weapon = nil,
			Armor = nil,
			Accessory = nil,
		},
	},
	Stats = {
		PlayTime = 0,
		LoginCount = 0,
		LastLogin = 0,
	},
	Settings = {
		MusicVolume = 0.5,
		SfxVolume = 0.5,
		GraphicsQuality = "Medium",
	},
	Achievements = {},
	Friends = {},
}

return ProfileTemplate
