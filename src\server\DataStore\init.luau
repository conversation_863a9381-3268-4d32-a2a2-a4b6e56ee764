
local ProfileManager = require(script.ProfileManager)
local ProfileTypes = require(game:GetService("ReplicatedStorage").Shared.Types.ProfileTypes)

type Profile = ProfileTypes.Profile
type PlayerData = ProfileTypes.PlayerData

local DataStore = {}

function DataStore.loadPlayerProfileAsync(player: Player): boolean
	local profile = ProfileManager.loadProfileAsync(player)
	
	if profile == nil then
		player:Kick("Failed to load profile - please rejoin")
		return false
	end
	
	return true
end

function DataStore.getPlayerProfile(player: Player): Profile?
	return ProfileManager.getProfile(player)
end

function DataStore.getPlayerData(player: Player): PlayerData?
	local profile = ProfileManager.getProfile(player)
	return profile and profile.Data
end

function DataStore.releasePlayerProfile(player: Player)
	ProfileManager.releaseProfile(player)
end

function DataStore.updatePlayerData(player: Player, updateFunction: (data: PlayerData) -> ())
	local profile = ProfileManager.getProfile(player)
	if profile and profile:IsActive() then
		updateFunction(profile.Data)
	end
end

function DataStore.addCash(player: Player, amount: number)
	DataStore.updatePlayerData(player, function(data)
		data.Cash += amount
	end)
end

function DataStore.removeCash(player: Player, amount: number): boolean
	local profile = ProfileManager.getProfile(player)
	if profile and profile:IsActive() and profile.Data.Cash >= amount then
		profile.Data.Cash -= amount
		return true
	end
	return false
end

function DataStore.addExperience(player: Player, amount: number)
	DataStore.updatePlayerData(player, function(data)
		data.Experience += amount
		local newLevel = math.floor(data.Experience / 1000) + 1
		if newLevel > data.Level then
			data.Level = newLevel
		end
	end)
end

function DataStore.addItem(player: Player, itemName: string, amount: number?)
	local actualAmount = amount or 1
	DataStore.updatePlayerData(player, function(data)
		data.Inventory.Items[itemName] = (data.Inventory.Items[itemName] or 0) + actualAmount
	end)
end

function DataStore.removeItem(player: Player, itemName: string, amount: number?): boolean
	local actualAmount = amount or 1
	local profile = ProfileManager.getProfile(player)
	if profile and profile:IsActive() then
		local currentAmount = profile.Data.Inventory.Items[itemName] or 0
		if currentAmount >= actualAmount then
			profile.Data.Inventory.Items[itemName] = currentAmount - actualAmount
			if profile.Data.Inventory.Items[itemName] <= 0 then
				profile.Data.Inventory.Items[itemName] = nil
			end
			return true
		end
	end
	return false
end

function DataStore.equipItem(player: Player, itemType: "Weapon" | "Armor" | "Accessory", itemName: string?)
	DataStore.updatePlayerData(player, function(data)
		data.Inventory.Equipment[itemType] = itemName
	end)
end

function DataStore.unlockAchievement(player: Player, achievementId: string)
	DataStore.updatePlayerData(player, function(data)
		data.Achievements[achievementId] = true
	end)
end

function DataStore.updateSettings(player: Player, settings: { [string]: any })
	DataStore.updatePlayerData(player, function(data)
		for key, value in settings do
			data.Settings[key] = value
		end
	end)
end

function DataStore.addFriend(player: Player, friendUserId: number)
	DataStore.updatePlayerData(player, function(data)
		data.Friends[friendUserId] = true
	end)
end

function DataStore.removeFriend(player: Player, friendUserId: number)
	DataStore.updatePlayerData(player, function(data)
		data.Friends[friendUserId] = nil
	end)
end

function DataStore.sendGlobalUpdate(targetUserId: number, updateType: string, updateData: { [string]: any })
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = updateType,
		Data = updateData,
	})
end

function DataStore.sendCashGift(targetUserId: number, amount: number)
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = "Cash",
		Amount = amount,
	})
end

function DataStore.sendItemGift(targetUserId: number, itemName: string, amount: number?)
	ProfileManager.sendGlobalUpdate(targetUserId, {
		Type = "Item",
		ItemName = itemName,
		Amount = amount or 1,
	})
end

function DataStore.getAllLoadedProfiles(): { [Player]: Profile }
	return ProfileManager.getAllProfiles()
end

function DataStore.shutdown()
	ProfileManager.shutdown()
end

return DataStore
