local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Janitor = require(ReplicatedStorage.Packages.Janitor)
local SwordTypes = require(ReplicatedStorage.Shared.Types.SwordTypes)

type SwordConfig = SwordTypes.SwordConfig
type SwordData = SwordTypes.SwordData

local SwordManager = {}

local PlayerSwordData: { [Player]: SwordData } = {}
local PlayerJanitors: { [Player]: typeof(Janitor.new()) } = {}
local SwordConfigs: { [string]: SwordConfig } = {}
local PointsProvider: ((player: Player) -> number)? = nil

local SWORDS_FOLDER = ReplicatedStorage:FindFirstChild("Swords")

local function loadSwordConfigs()
	if not SWORDS_FOLDER then
		local swordsFolder = Instance.new("Folder")
		swordsFolder.Name = "Swords"
		swordsFolder.Parent = ReplicatedStorage

		local defaultSword = Instance.new("Model")
		defaultSword.Name = "0"
		defaultSword.Parent = swordsFolder

		local handle = Instance.new("Part")
		handle.Name = "Handle"
		handle.Size = Vector3.new(0.4, 0.4, 4)
		handle.Material = Enum.Material.Neon
		handle.BrickColor = BrickColor.new("Bright blue")
		handle.Parent = defaultSword

		SWORDS_FOLDER = swordsFolder
	end

	for _, swordModel in SWORDS_FOLDER:GetChildren() do
		if swordModel:IsA("Model") then
			local pointsRequired = tonumber(swordModel.Name)
			if pointsRequired then
				SwordConfigs[swordModel.Name] = {
					PointsRequired = pointsRequired,
					HitboxScale = math.max(1, pointsRequired / 250 + 1),
					Model = swordModel,
				}
			end
		end
	end
end

local function getBestSwordForPoints(points: number): string?
	local bestSword = nil
	local highestPoints = -1
	
	for swordName, config in SwordConfigs do
		if config.PointsRequired <= points and config.PointsRequired > highestPoints then
			highestPoints = config.PointsRequired
			bestSword = swordName
		end
	end
	
	return bestSword
end

local function createAttachments(handle: Part, scale: number)
	local rightGrip = Instance.new("Attachment")
	rightGrip.Name = "RightGripAttachment"
	rightGrip.Position = Vector3.new(0, 0, -handle.Size.Z * 0.4)
	rightGrip.Parent = handle

	local blade = Instance.new("Attachment")
	blade.Name = "BladeAttachment"
	blade.Position = Vector3.new(0, 0, handle.Size.Z * 0.4)
	blade.Parent = handle

	local guard = Instance.new("Attachment")
	guard.Name = "GuardAttachment"
	guard.Position = Vector3.new(0, 0, -handle.Size.Z * 0.2)
	guard.Parent = handle

	local pommel = Instance.new("Attachment")
	pommel.Name = "PommelAttachment"
	pommel.Position = Vector3.new(0, 0, -handle.Size.Z * 0.5)
	pommel.Parent = handle
end

local function createToolGrip(tool: Tool, handle: Part)
	tool.Grip = CFrame.new(0, 0, -handle.Size.Z * 0.4) * CFrame.Angles(math.rad(-90), 0, 0)
	tool.GripForward = Vector3.new(0, 0, 1)
	tool.GripRight = Vector3.new(1, 0, 0)
	tool.GripUp = Vector3.new(0, 1, 0)
end

local function createSwordTool(swordName: string): Tool?
	local config = SwordConfigs[swordName]
	if not config then
		return nil
	end

	local tool = Instance.new("Tool")
	tool.Name = `Sword_{swordName}`
	tool.RequiresHandle = true
	tool.CanBeDropped = false
	tool.ToolTip = `Sword (${config.PointsRequired} points)`

	local handle = config.Model:FindFirstChild("Handle")
	if handle then
		local newHandle = handle:Clone()
		newHandle.Parent = tool

		createAttachments(newHandle, config.HitboxScale)
		createToolGrip(tool, newHandle)

		local hitbox = Instance.new("Part")
		hitbox.Name = "Hitbox"
		hitbox.Anchored = false
		hitbox.CanCollide = false
		hitbox.Transparency = 1
		hitbox.Size = newHandle.Size * config.HitboxScale
		hitbox.CFrame = newHandle.CFrame
		hitbox.Parent = tool

		local weld = Instance.new("WeldConstraint")
		weld.Part0 = newHandle
		weld.Part1 = hitbox
		weld.Parent = newHandle

		local swordScript = script.Parent.SwordScript:Clone()
		swordScript.Parent = tool

		local pointsValue = Instance.new("NumberValue")
		pointsValue.Name = "PointsRequired"
		pointsValue.Value = config.PointsRequired
		pointsValue.Parent = tool

		local scaleValue = Instance.new("NumberValue")
		scaleValue.Name = "HitboxScale"
		scaleValue.Value = config.HitboxScale
		scaleValue.Parent = tool
	else
		return nil
	end

	return tool
end

local function equipSword(player: Player, swordName: string)
	local playerData = PlayerSwordData[player]
	if not playerData then
		return
	end

	local janitor = PlayerJanitors[player]
	if not janitor then
		return
	end

	if playerData.EquippedTool then
		playerData.EquippedTool:Destroy()
		playerData.EquippedTool = nil
	end

	local tool = createSwordTool(swordName)
	if tool then
		playerData.CurrentSword = swordName
		playerData.EquippedTool = tool
		tool.Parent = player.Backpack

		janitor:Add(tool, "Destroy", "CurrentSword")
	end
end

local function updatePlayerSword(player: Player)
	if not PointsProvider then
		return
	end

	local totalPoints = PointsProvider(player)

	local bestSword = getBestSwordForPoints(totalPoints)
	if not bestSword then
		bestSword = "0"
	end

	local currentData = PlayerSwordData[player]
	if currentData and currentData.CurrentSword ~= bestSword then
		equipSword(player, bestSword)
	end
end

function SwordManager.setupPlayer(player: Player)
	local janitor = Janitor.new()
	PlayerJanitors[player] = janitor

	PlayerSwordData[player] = {
		CurrentSword = "",
		EquippedTool = nil,
	}

	janitor:Add(function()
		local playerData = PlayerSwordData[player]
		if playerData and playerData.EquippedTool then
			playerData.EquippedTool:Destroy()
		end
		PlayerSwordData[player] = nil
	end, true, "Cleanup")

	updatePlayerSword(player)

	janitor:Add(RunService.Heartbeat:Connect(function()
		updatePlayerSword(player)
	end), "Disconnect", "SwordUpdateLoop")
end

function SwordManager.cleanupPlayer(player: Player)
	local janitor = PlayerJanitors[player]
	if janitor then
		janitor:Cleanup()
		PlayerJanitors[player] = nil
	end
end

function SwordManager.getSwordConfig(swordName: string): SwordConfig?
	return SwordConfigs[swordName]
end

function SwordManager.getCurrentSword(player: Player): string?
	local playerData = PlayerSwordData[player]
	return playerData and playerData.CurrentSword
end

function SwordManager.initialize()
	loadSwordConfigs()
end

function SwordManager.setPointsProvider(provider: (player: Player) -> number)
	PointsProvider = provider
end

return SwordManager
