local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Janitor = require(ReplicatedStorage.Packages.Janitor)
local SwordTypes = require(ReplicatedStorage.Shared.Types.SwordTypes)

type SwordConfig = SwordTypes.SwordConfig
type SwordData = SwordTypes.SwordData

local SwordManager = {}

local PlayerSwordData: { [Player]: SwordData } = {}
local PlayerJanitors: { [Player]: typeof(Janitor.new()) } = {}
local SwordConfigs: { [string]: SwordConfig } = {}
local PointsProvider: ((player: Player) -> number)? = nil

local SWORDS_FOLDER = ReplicatedStorage:FindFirstChild("Swords")

local function loadSwordConfigs()
	if not SWORDS_FOLDER then
		warn("Swords folder not found in ReplicatedStorage! Creating default sword...")
		local swordsFolder = Instance.new("Folder")
		swordsFolder.Name = "Swords"
		swordsFolder.Parent = ReplicatedStorage

		local defaultSword = Instance.new("Model")
		defaultSword.Name = "0"
		defaultSword.Parent = swordsFolder

		local handle = Instance.new("Part")
		handle.Name = "Handle"
		handle.Size = Vector3.new(0.4, 0.4, 4)
		handle.Material = Enum.Material.Neon
		handle.BrickColor = BrickColor.new("Bright blue")
		handle.Parent = defaultSword

		SWORDS_FOLDER = swordsFolder
		print("Created default sword folder and sword")
	end

	print(`Loading sword configs from {SWORDS_FOLDER.Name}...`)
	for _, swordModel in SWORDS_FOLDER:GetChildren() do
		if swordModel:IsA("Model") then
			local pointsRequired = tonumber(swordModel.Name)
			if pointsRequired then
				SwordConfigs[swordModel.Name] = {
					PointsRequired = pointsRequired,
					HitboxScale = math.max(1, pointsRequired / 250 + 1),
					Model = swordModel,
				}
				print(`Loaded sword config: {swordModel.Name} (requires {pointsRequired} points)`)
			end
		end
	end
	local count = 0
	for _ in SwordConfigs do
		count += 1
	end
	print(`Total sword configs loaded: {count}`)
end

local function getBestSwordForPoints(points: number): string?
	local bestSword = nil
	local highestPoints = -1
	
	for swordName, config in SwordConfigs do
		if config.PointsRequired <= points and config.PointsRequired > highestPoints then
			highestPoints = config.PointsRequired
			bestSword = swordName
		end
	end
	
	return bestSword
end

local function createSwordTool(swordName: string): Tool?
	print(`Creating sword tool for: {swordName}`)

	local config = SwordConfigs[swordName]
	if not config then
		print(`No config found for sword: {swordName}`)
		return nil
	end

	local tool = Instance.new("Tool")
	tool.Name = `Sword_{swordName}`
	tool.RequiresHandle = true
	tool.CanBeDropped = false

	local handle = config.Model:FindFirstChild("Handle")
	if handle then
		print(`Found handle for sword: {swordName}`)
		local newHandle = handle:Clone()
		newHandle.Parent = tool

		local hitbox = Instance.new("Part")
		hitbox.Name = "Hitbox"
		hitbox.Anchored = false
		hitbox.CanCollide = false
		hitbox.Transparency = 1
		hitbox.Size = newHandle.Size * config.HitboxScale
		hitbox.CFrame = newHandle.CFrame
		hitbox.Parent = tool

		local weld = Instance.new("WeldConstraint")
		weld.Part0 = newHandle
		weld.Part1 = hitbox
		weld.Parent = newHandle

		local swordScript = script.Parent.SwordScript:Clone()
		swordScript.Parent = tool
		swordScript.Enabled = true

		local pointsValue = Instance.new("NumberValue")
		pointsValue.Name = "PointsRequired"
		pointsValue.Value = config.PointsRequired
		pointsValue.Parent = tool

		local scaleValue = Instance.new("NumberValue")
		scaleValue.Name = "HitboxScale"
		scaleValue.Value = config.HitboxScale
		scaleValue.Parent = tool

		print(`Successfully created tool: {tool.Name}`)
	else
		print(`No handle found for sword: {swordName}`)
		return nil
	end

	return tool
end

local function equipSword(player: Player, swordName: string)
	print(`Equipping sword {swordName} for {player.Name}`)

	local playerData = PlayerSwordData[player]
	if not playerData then
		print(`No player data found for {player.Name}`)
		return
	end

	local janitor = PlayerJanitors[player]
	if not janitor then
		print(`No janitor found for {player.Name}`)
		return
	end

	if playerData.EquippedTool then
		print(`Destroying old tool for {player.Name}`)
		playerData.EquippedTool:Destroy()
		playerData.EquippedTool = nil
	end

	local tool = createSwordTool(swordName)
	if tool then
		print(`Created tool {tool.Name} for {player.Name}`)
		playerData.CurrentSword = swordName
		playerData.EquippedTool = tool
		tool.Parent = player.Backpack

		janitor:Add(tool, "Destroy", "CurrentSword")
		print(`Successfully equipped {swordName} for {player.Name}`)
	else
		print(`Failed to create tool for sword {swordName}`)
	end
end

local function updatePlayerSword(player: Player)
	if not PointsProvider then
		print(`No points provider set for {player.Name}`)
		return
	end

	local totalPoints = PointsProvider(player)
	print(`{player.Name} has {totalPoints} total points`)

	local bestSword = getBestSwordForPoints(totalPoints)
	if not bestSword then
		bestSword = "0"
	end
	print(`Best sword for {player.Name}: {bestSword}`)

	local currentData = PlayerSwordData[player]
	if currentData and currentData.CurrentSword ~= bestSword then
		print(`Switching {player.Name} from {currentData.CurrentSword} to {bestSword}`)
		equipSword(player, bestSword)
	end
end

function SwordManager.setupPlayer(player: Player)
	print(`Setting up sword system for {player.Name}`)

	local janitor = Janitor.new()
	PlayerJanitors[player] = janitor

	PlayerSwordData[player] = {
		CurrentSword = "0",
		EquippedTool = nil,
	}

	janitor:Add(function()
		local playerData = PlayerSwordData[player]
		if playerData and playerData.EquippedTool then
			playerData.EquippedTool:Destroy()
		end
		PlayerSwordData[player] = nil
	end, true, "Cleanup")

	print(`Initial sword update for {player.Name}`)
	updatePlayerSword(player)

	janitor:Add(RunService.Heartbeat:Connect(function()
		updatePlayerSword(player)
	end), "Disconnect", "SwordUpdateLoop")

	print(`Sword system setup complete for {player.Name}`)
end

function SwordManager.cleanupPlayer(player: Player)
	local janitor = PlayerJanitors[player]
	if janitor then
		janitor:Cleanup()
		PlayerJanitors[player] = nil
	end
end

function SwordManager.getSwordConfig(swordName: string): SwordConfig?
	return SwordConfigs[swordName]
end

function SwordManager.getCurrentSword(player: Player): string?
	local playerData = PlayerSwordData[player]
	return playerData and playerData.CurrentSword
end

function SwordManager.initialize()
	loadSwordConfigs()
end

function SwordManager.setPointsProvider(provider: (player: Player) -> number)
	PointsProvider = provider
end

return SwordManager
