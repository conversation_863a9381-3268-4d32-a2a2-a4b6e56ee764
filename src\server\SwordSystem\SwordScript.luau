local Players = game:GetService("Players")

local CombatHandler = require(script.Parent.CombatHandler)

local tool = script.Parent
local handle = tool:WaitF<PERSON><PERSON>hil<PERSON>("Handle")
local hitbox = tool:WaitForChild("Hitbox")

local pointsRequired = tool:WaitFor<PERSON>hild("PointsRequired").Value
local hitboxScale = tool:WaitForChild("HitboxScale").Value

local lastHit = {}
local HIT_COOLDOWN = 0.5

local function onHit(hit: BasePart)
	if not hit or not hit.Parent then
		return
	end

	local hitCharacter = hit.Parent
	local hitHumanoid = hitCharacter:FindFirstChild("Humanoid")
	local hitPlayer = Players:GetPlayerFromCharacter(hitCharacter)

	if not hitPlayer or not hitHumanoid then
		return
	end

	local toolOwner = tool.Parent and tool.Parent.Parent
	local ownerPlayer = Players:GetPlayerFromCharacter(toolOwner)

	if not ownerPlayer or hitPlayer == ownerPlayer then
		return
	end

	local currentTime = tick()
	if lastHit[hitPlayer] and currentTime - lastHit[hitPlayer] < HIT_COOLDOWN then
		return
	end

	lastHit[hitPlayer] = currentTime

	CombatHandler.handleSwordHit(ownerPlayer, hit, tostring(pointsRequired))
end

local function onEquipped()
	local connection = hitbox.Touched:Connect(onHit)

	tool.Unequipped:Connect(function()
		connection:Disconnect()
	end)
end

tool.Equipped:Connect(onEquipped)
