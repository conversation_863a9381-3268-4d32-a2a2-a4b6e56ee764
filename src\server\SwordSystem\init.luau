local Players = game:GetService("Players")

local SwordManager = require(script.SwordManager)
local CombatHandler = require(script.CombatHandler)
local RemoteEvents = require(script.RemoteEvents)

local SwordSystem = {}

local remotes = nil

local function onPlayerAdded(player: Player)
	SwordManager.setupPlayer(player)
end

local function onPlayerRemoving(player: Player)
	SwordManager.cleanupPlayer(player)
end

local function onSwordHit(player: Player, hitPart: BasePart, swordName: string)
	CombatHandler.handleSwordHit(player, hitPart, swordName)
end

function SwordSystem.initialize()
	SwordManager.initialize()
	remotes = RemoteEvents.initialize()

	remotes.SwordHit.OnServerEvent:Connect(onSwordHit)

	Players.PlayerAdded:Connect(onPlayerAdded)
	Players.PlayerRemoving:Connect(onPlayerRemoving)

	for _, player in Players:GetPlayers() do
		task.spawn(onPlayerAdded, player)
	end
end

function SwordSystem.setDataStoreProvider(provider: any)
	CombatHandler.setDataStoreProvider(provider)
end

function SwordSystem.getSwordManager()
	return SwordManager
end

function SwordSystem.getCombatHandler()
	return CombatHandler
end

return SwordSystem
