local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local DataStore = require(script.DataStore)

local function onPlayerAdded(player: Player)
	DataStore.loadPlayerProfileAsync(player)
end

local function onPlayerRemoving(player: Player)
	DataStore.releasePlayerProfile(player)
end

Players.PlayerAdded:Connect(onPlayerAdded)
Players.PlayerRemoving:Connect(onPlayerRemoving)

for _, player in Players:GetPlayers() do
	task.spawn(onPlayerAdded, player)
end

if RunService:IsStudio() then
	game:BindToClose(function()
		DataStore.shutdown()
	end)
end