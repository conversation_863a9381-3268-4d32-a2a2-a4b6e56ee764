export type PlayerData = {
	TotalPoints: number,
	Gold: number,
	Gems: number,
	Level: number,
	Experience: number,
	Inventory: {
		Items: { [string]: number },
		Equipment: {
			Weapon: string?,
			Armor: string?,
			Accessory: string?,
		},
	},
	Stats: {
		PlayTime: number,
		LoginCount: number,
		LastLogin: number,
	},
	Settings: {
		MusicVolume: number,
		SfxVolume: number,
		GraphicsQuality: "Low" | "Medium" | "High" | "Ultra",
	},
	Achievements: { [string]: boolean },
	Friends: { [number]: boolean },
}

export type ProfileMetaData = {
	ProfileCreateTime: number,
	SessionLoadCount: number,
	ActiveSession: { [number]: number | string }?,
	ForceLoadSession: { [number]: number | string }?,
	MetaTags: { [string]: any },
	LastUpdate: number,
}

export type GlobalUpdate = {
	Type: string,
	Data: { [string]: any },
}

export type Profile = {
	Data: PlayerData,
	MetaData: ProfileMetaData,
	MetaTagsUpdated: RBXScriptSignal,
	RobloxMetaData: { [string]: any },
	UserIds: { number },
	KeyInfo: DataStoreKeyInfo,
	KeyInfoUpdated: RBXScriptSignal,
	GlobalUpdates: any,
	IsActive: (self: Profile) -> boolean,
	GetMetaTag: (self: Profile, tagName: string) -> any,
	Reconcile: (self: Profile) -> (),
	ListenToRelease: (self: Profile, listener: (placeId: number?, gameJobId: string?) -> ()) -> RBXScriptConnection,
	Release: (self: Profile) -> (),
	ListenToHopReady: (self: Profile, listener: () -> ()) -> RBXScriptConnection,
	AddUserId: (self: Profile, userId: number) -> (),
	RemoveUserId: (self: Profile, userId: number) -> (),
	Identify: (self: Profile) -> string,
	SetMetaTag: (self: Profile, tagName: string, value: any) -> (),
	Save: (self: Profile) -> (),
}

return {}
